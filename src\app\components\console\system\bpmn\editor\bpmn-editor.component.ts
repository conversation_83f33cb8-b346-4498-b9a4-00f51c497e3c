import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, AfterViewInit, ViewChild, ElementRef } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NzMessageService } from 'ng-zorro-antd/message';
import { BpmnService } from 'src/app/service/console/system/bpmn.service';

// 导入bpmn-js相关模块
import BpmnModeler from 'bpmn-js/lib/Modeler';
import { BpmnPropertiesPanelModule } from 'bpmn-js-properties-panel';

// 导入中文翻译模块
import TranslateModule from 'src/app/utils/bpmn-translate';

// 导入自定义属性提供者
import CustomPropertiesProviderModule from 'src/app/utils/custom-properties-provider';

@Component({
    selector: 'app-bpmn-editor',
    templateUrl: './bpmn-editor.component.html',
    styleUrls: ['./bpmn-editor.component.less']
})
export class BpmnEditorComponent implements OnInit, AfterViewInit, On<PERSON><PERSON><PERSON> {
    @ViewChild('bpmnContainer', { static: false }) bpmnContainer!: ElementRef;
    @ViewChild('propertiesPanel', { static: false }) propertiesPanel!: ElementRef;

    private bpmnModeler: any;
    public processId: string | null = null;
    public processData: any = null;
    public loading = false;
    public saving = false;

    // 默认的BPMN XML模板
    private defaultBpmnXml = `<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.12.0">
  <bpmn:process id="Process_1" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_1">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="179" y="79" width="36" height="36" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>`;

    constructor(
        private route: ActivatedRoute,
        private router: Router,
        private msg: NzMessageService,
        private bpmnService: BpmnService
    ) {}

    ngOnInit(): void {
        // 获取路由参数
        this.processId = this.route.snapshot.paramMap.get('id');

        if (this.processId) {
            this.loadProcessData();
        }
    }

    ngAfterViewInit(): void {
        setTimeout(() => {
            this.initBpmnModeler();
        }, 100);
    }

    ngOnDestroy(): void {
        if (this.bpmnModeler) {
            this.bpmnModeler.destroy();
        }
    }

    // 加载流程数据
    private async loadProcessData(): Promise<void> {
        if (!this.processId) return;

        try {
            this.loading = true;
            const response = await this.bpmnService.getById(Number(this.processId)).then(res => {
                if (response.success) {
                    this.processData = response.data;

                    // 如果BPMN建模器已初始化，导入XML
                    if (this.bpmnModeler) {
                        this.importBpmnXml(this.processData.bpmnXml || this.defaultBpmnXml);
                    }
                } else {
                    this.msg.error('加载流程数据失败');
                }
            }).catch(err => {
                this.msg.error('获取流程列表失败');
                console.error(err);
            });
        } catch (error) {
            console.error('加载流程数据失败:', error);
            this.msg.error('加载流程数据失败');
        } finally {
            this.loading = false;
        }
    }

    // 初始化BPMN建模器
    private initBpmnModeler(): void {
        if (!this.bpmnContainer || !this.propertiesPanel) {
            return;
        }

        try {
            // 创建BPMN建模器实例
            this.bpmnModeler = new BpmnModeler({
                container: this.bpmnContainer.nativeElement,
                propertiesPanel: {
                    parent: this.propertiesPanel.nativeElement
                },
                additionalModules: [
                    BpmnPropertiesPanelModule,
                    CustomPropertiesProviderModule,
                    TranslateModule
                ],
                keyboard: {
                    bindTo: window
                }
            });

            // 导入BPMN XML
            const xmlToImport = this.processData?.bpmnXml || this.defaultBpmnXml;
            this.importBpmnXml(xmlToImport);

            // 监听建模器事件
            this.setupEventListeners();

        } catch (error) {
            console.error('初始化BPMN建模器失败:', error);
            this.msg.error('初始化BPMN建模器失败');
        }
    }

    // 设置事件监听器
    private setupEventListeners(): void {
        if (!this.bpmnModeler) return;

        // 监听元素变化事件
        this.bpmnModeler.on('commandStack.changed', () => {
            // 可以在这里添加自动保存逻辑
        });

        // 监听元素选择事件，用于自定义属性面板
        this.bpmnModeler.on('selection.changed', (event) => {
            setTimeout(() => {
                this.customizePropertiesPanel(event.newSelection);
            }, 200);
        });
    }

    // 自定义属性面板显示
    private customizePropertiesPanel(selectedElements: any[]): void {
        const propertiesPanel = this.propertiesPanel?.nativeElement;
        if (!propertiesPanel) return;

        // 如果选中的是流程元素，则完全替换属性面板内容
        if (selectedElements && selectedElements.length === 1) {
            const element = selectedElements[0];
            if (element.type === 'bpmn:Process') {
                setTimeout(() => {
                    this.replacePropertiesPanelContent(element);
                }, 100);
                return;
            }
        }

        // 对于其他元素，隐藏不需要的属性组
        const groupsToHide = [
            '历史清除', '任务列表', '候选启动器', '外部任务',
            '执行作业', '执行监听器', '扩展属性'
        ];

        groupsToHide.forEach(groupName => {
            const groups = propertiesPanel.querySelectorAll('.bpp-properties-group');
            groups.forEach((group: any) => {
                const header = group.querySelector('.bpp-properties-group-header');
                if (header && header.textContent && header.textContent.includes(groupName)) {
                    group.style.display = 'none';
                }
            });
        });
    }

    // 替换属性面板内容为自定义的常规信息
    private replacePropertiesPanelContent(element: any): void {
        const propertiesPanel = this.propertiesPanel?.nativeElement;
        if (!propertiesPanel) return;

        // 清空现有内容
        propertiesPanel.innerHTML = '';

        // 创建自定义的常规信息组
        const generalGroup = document.createElement('div');
        generalGroup.className = 'bpp-properties-group';
        generalGroup.innerHTML = `
            <div class="bpp-properties-group-header" style="background: #f0f2f5; font-weight: 600; color: #1890ff; padding: 12px 16px; border-bottom: 1px solid #e8e8e8;">
                常规信息
            </div>
            <div class="bpp-properties-group-entries" style="padding: 16px;">
                <div class="bpp-field" style="margin-bottom: 16px;">
                    <label for="process-id" style="display: block; margin-bottom: 4px; font-weight: 500;">ID <span style="color: red;">*</span></label>
                    <input id="process-id" type="text" value="${element.businessObject.id || ''}"
                           style="width: 100%; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;"
                           placeholder="请输入流程ID" />
                </div>
                <div class="bpp-field" style="margin-bottom: 16px;">
                    <label for="process-name" style="display: block; margin-bottom: 4px; font-weight: 500;">名称 <span style="color: red;">*</span></label>
                    <input id="process-name" type="text" value="${element.businessObject.name || ''}"
                           style="width: 100%; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;"
                           placeholder="请输入流程名称" />
                </div>
            </div>
        `;

        propertiesPanel.appendChild(generalGroup);

        // 绑定事件处理器
        this.bindCustomFieldEvents(element);
    }

    // 绑定自定义字段的事件处理器
    private bindCustomFieldEvents(element: any): void {
        const idInput = document.getElementById('process-id') as HTMLInputElement;
        const nameInput = document.getElementById('process-name') as HTMLInputElement;

        if (idInput) {
            idInput.addEventListener('input', (event) => {
                const target = event.target as HTMLInputElement;
                element.businessObject.id = target.value;
            });
        }

        if (nameInput) {
            nameInput.addEventListener('input', (event) => {
                const target = event.target as HTMLInputElement;
                element.businessObject.name = target.value;
            });
        }
    }

    // 导入BPMN XML
    private async importBpmnXml(xml: string): Promise<void> {
        try {
            await this.bpmnModeler.importXML(xml);

            // 自适应画布大小
            const canvas = this.bpmnModeler.get('canvas');
            canvas.zoom('fit-viewport');

        } catch (error) {
            console.error('导入BPMN XML失败:', error);
            this.msg.error('导入BPMN XML失败');
        }
    }

    // 保存流程
    async save(): Promise<void> {
        try {
            this.saving = true;

            // 获取BPMN XML
            const result = await this.bpmnModeler.saveXML({ format: true });
            const bpmnXml = result.xml;

            // 获取SVG图像
            const svgResult = await this.bpmnModeler.saveSVG();
            const svgImage = svgResult.svg;

            // 准备保存数据
            const saveData = {
                id: this.processId,
                bpmnXml: bpmnXml,
                svgImage: svgImage
            };

            // 调用服务保存
            const response = await this.bpmnService.saveProcess(saveData);

            if (response.success) {
                this.msg.success('保存成功');
            } else {
                this.msg.error(response.message || '保存失败');
            }

        } catch (error) {
            console.error('保存流程失败:', error);
            this.msg.error('保存流程失败');
        } finally {
            this.saving = false;
        }
    }

    // 下载BPMN文件
    async downloadBpmn(): Promise<void> {
        try {
            const result = await this.bpmnModeler.saveXML({ format: true });
            const xml = result.xml;

            const blob = new Blob([xml], { type: 'application/xml' });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `${this.processData?.name || 'process'}.bpmn`;
            link.click();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('下载BPMN文件失败:', error);
            this.msg.error('下载BPMN文件失败');
        }
    }

    // 下载SVG图像
    async downloadSvg(): Promise<void> {
        try {
            const result = await this.bpmnModeler.saveSVG();
            const svg = result.svg;

            const blob = new Blob([svg], { type: 'image/svg+xml' });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `${this.processData?.name || 'process'}.svg`;
            link.click();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('下载SVG图像失败:', error);
            this.msg.error('下载SVG图像失败');
        }
    }

    // 返回列表
    goBack(): void {
        this.router.navigate(['/console/system/bpmn']);
    }

    // 缩放到适合视口
    zoomToFit(): void {
        if (this.bpmnModeler) {
            const canvas = this.bpmnModeler.get('canvas');
            canvas.zoom('fit-viewport');
        }
    }

    // 重置缩放
    zoomReset(): void {
        if (this.bpmnModeler) {
            const canvas = this.bpmnModeler.get('canvas');
            canvas.zoom(1);
        }
    }
}