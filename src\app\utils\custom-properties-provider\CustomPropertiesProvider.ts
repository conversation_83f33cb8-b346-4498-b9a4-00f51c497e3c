import { is } from 'bpmn-js/lib/util/ModelUtil';

/**
 * 自定义属性提供者
 * 用于定义BPMN元素的属性面板内容
 */
export default function CustomPropertiesProvider(propertiesPanel, translate) {

  // 注册属性提供者，优先级设置为高，以覆盖默认提供者
  this.getGroups = function(element) {

    // 只为流程(Process)元素添加自定义属性
    if (is(element, 'bpmn:Process')) {
      // 返回自定义组，替换所有默认组
      return [
        createGeneralGroup(element, translate)
      ];
    }

    // 对于其他元素，返回空数组（不显示任何属性）
    return [];
  };

  // 设置高优先级以覆盖默认提供者
  propertiesPanel.registerProvider(500, this);
}

CustomPropertiesProvider.$inject = ['propertiesPanel', 'translate'];

/**
 * 创建常规信息组
 */
function createGeneralGroup(element, translate) {
  const entries = [];

  // ID字段
  entries.push({
    id: 'process-id',
    label: translate('ID'),
    modelProperty: 'id',
    get: function() {
      return { id: element.businessObject.id || '' };
    },
    set: function(element, values) {
      element.businessObject.id = values.id;
    },
    validate: function(element, values) {
      const validation: any = {};
      if (!values.id || values.id.trim() === '') {
        validation.id = translate('ID不能为空');
      }
      return validation;
    }
  });

  // 名称字段
  entries.push({
    id: 'process-name',
    label: translate('名称'),
    modelProperty: 'name',
    get: function() {
      return { name: element.businessObject.name || '' };
    },
    set: function(element, values) {
      element.businessObject.name = values.name;
    },
    validate: function(element, values) {
      const validation: any = {};
      if (!values.name || values.name.trim() === '') {
        validation.name = translate('名称不能为空');
      }
      return validation;
    }
  });

  return {
    id: 'general',
    label: translate('常规信息'),
    entries: entries
  };
}
