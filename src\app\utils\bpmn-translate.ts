// BPMN.js 中文翻译模块
import bpmn from 'bpmn-js-i18n-zh/lib/bpmn-js';
import properties from 'bpmn-js-i18n-zh/lib/properties-panel';
import camunda from 'bpmn-js-i18n-zh/lib/camunda-properties-panel';
import zeebe from 'bpmn-js-i18n-zh/lib/zeebe-properties-panel';

// 合并所有翻译资源
const zhCN = {
  ...bpmn,
  ...properties,
  ...camunda,
  ...zeebe,
  // 可以在这里添加自定义翻译内容
  // 自定义属性面板翻译
  '常规信息': '常规信息',
  'ID': 'ID',
  '名称': '名称',
  'ID不能为空': 'ID不能为空',
  '名称不能为空': '名称不能为空',
  'Create StartEvent': '创建开始事件',
  'Create EndEvent': '创建结束事件',
  'Create Task': '创建任务',
  'Create UserTask': '创建用户任务',
  'Create ServiceTask': '创建服务任务',
  'Create Gateway': '创建网关',
  'Create ExclusiveGateway': '创建排他网关',
  'Create ParallelGateway': '创建并行网关',
  'Create InclusiveGateway': '创建包容网关',
  'Create SubProcess': '创建子流程',
  'Create Pool/Participant': '创建池/参与者',
  'Create Lane': '创建泳道',
  'Create DataObject': '创建数据对象',
  'Create DataStore': '创建数据存储',
  'Create Group': '创建组',
  'Create TextAnnotation': '创建文本注释',
  'Append Task': '追加任务',
  'Append UserTask': '追加用户任务',
  'Append ServiceTask': '追加服务任务',
  'Append Gateway': '追加网关',
  'Append EndEvent': '追加结束事件',
  'Change type': '更改类型',
  'Connect using Sequence/MessageFlow or Association': '使用顺序流/消息流或关联连接',
  'Remove': '删除',
  'Activate the hand tool': '激活手型工具',
  'Activate the lasso tool': '激活套索工具',
  'Activate the create/remove space tool': '激活创建/删除空间工具',
  'Activate the global connect tool': '激活全局连接工具',
  'Edit label': '编辑标签',
  'Add Lane above': '在上方添加泳道',
  'Add Lane below': '在下方添加泳道',
  'Divide into two Lanes': '分为两个泳道',
  'Divide into three Lanes': '分为三个泳道',
  'Replace with expanded SubProcess': '替换为展开的子流程',
  'Replace with collapsed SubProcess': '替换为折叠的子流程',
  'Replace with EventSubProcess': '替换为事件子流程',

  // Properties Panel 相关翻译
  'Properties': '属性',
  'Properties Panel': '属性面板',
  'Element Properties': '元素属性',
  'Process Properties': '流程属性',
  'Task Properties': '任务属性',
  'Gateway Properties': '网关属性',
  'Event Properties': '事件属性',
  'Sequence Flow Properties': '顺序流属性',
  'Pool Properties': '池属性',
  'Lane Properties': '泳道属性',
  'Data Object Properties': '数据对象属性',
  'Data Store Properties': '数据存储属性',
  'Group Properties': '组属性',
  'Text Annotation Properties': '文本注释属性',

  // 常见的属性面板标题
  'StartEvent': '开始事件',
  'EndEvent': '结束事件',
  'Task': '任务',
  'UserTask': '用户任务',
  'ServiceTask': '服务任务',
  'ScriptTask': '脚本任务',
  'BusinessRuleTask': '业务规则任务',
  'ManualTask': '手工任务',
  'ReceiveTask': '接收任务',
  'SendTask': '发送任务',
  'CallActivity': '调用活动',
  'SubProcess': '子流程',
  'ExclusiveGateway': '排他网关',
  'ParallelGateway': '并行网关',
  'InclusiveGateway': '包容网关',
  'EventBasedGateway': '事件网关',
  'ComplexGateway': '复杂网关',
  'IntermediateCatchEvent': '中间捕获事件',
  'IntermediateThrowEvent': '中间抛出事件',
  'BoundaryEvent': '边界事件',
  'SequenceFlow': '顺序流',
  'MessageFlow': '消息流',
  'Association': '关联',
  'DataFlow': '数据流',
  'Pool': '池',
  'Lane': '泳道',
  'Participant': '参与者',
  'DataObject': '数据对象',
  'DataStore': '数据存储',
  'Group': '组',
  'TextAnnotation': '文本注释',
  'Collaboration': '协作',
  'Process': '流程',

  // 更多可能的标题翻译
  'Element': '元素',
  'Selected Element': '选中元素',
  'Multiple Elements': '多个元素',
  'No Element Selected': '未选择元素',
  'Canvas': '画布',
  'Diagram': '图表',
  'BPMN Diagram': 'BPMN图表',
  'Modeler': '建模器',
  'Modeling': '建模',
  'Edit': '编辑',
  'Editor': '编辑器',
  'Panel': '面板',
  'Property': '属性',
  'Property Panel': '属性面板',
  'Element Property': '元素属性',
  'Configuration': '配置',
  'Settings': '设置',
  'Details': '详情',
  'Information': '信息',
  'Metadata': '元数据',
  'Attributes': '特性',
  'Parameters': '参数',
  'Options': '选项',
  'Values': '值',
  'Fields': '字段',
  'Form': '表单',
  'Data': '数据',
  'Variables': '变量',
  'Extensions': '扩展',
  'Custom': '自定义',
  'Advanced': '高级',
  'Basic': '基础',
  'General': '通用',
  'Specific': '特定',
  'Type': '类型',
  'Category': '类别',
  'Classification': '分类',

  // 可能的其他标题翻译
  'Open documentation': '打开文档',
  'Toggle section': '切换部分',
  'Toggle list item': '切换列表项',
  'Delete item': '删除项目',
  'Create new list item': '创建新列表项',
  'Create': '创建',
  'List contains {numOfItems} item': '列表包含 {numOfItems} 项',
  'List contains {numOfItems} items': '列表包含 {numOfItems} 项',
  'Section contains an error': '部分包含错误',
  'Section contains data': '部分包含数据',

  // 可能的弹出窗口标题
  'Popup': '弹出窗口',
  'Dialog': '对话框',
  'Modal': '模态框',
  'Window': '窗口',
  'Editor Window': '编辑器窗口',
  'Property Editor': '属性编辑器',
  'Element Editor': '元素编辑器',
  'BPMN Element': 'BPMN元素',
  'BPMN Properties': 'BPMN属性',
  'Element Details': '元素详情',
  'Configuration Panel': '配置面板',
  'Settings Panel': '设置面板',
  'Property Configuration': '属性配置',
  'Element Configuration': '元素配置',
  'BPMN Configuration': 'BPMN配置',

  // Properties Panel 标题翻译（大写形式）
  'START EVENT': '开始事件',
  'END EVENT': '结束事件',
  'INTERMEDIATE CATCH EVENT': '中间捕获事件',
  'INTERMEDIATE THROW EVENT': '中间抛出事件',
  'BOUNDARY EVENT': '边界事件',
  'USER TASK': '用户任务',
  'SERVICE TASK': '服务任务',
  'SCRIPT TASK': '脚本任务',
  'BUSINESS RULE TASK': '业务规则任务',
  'MANUAL TASK': '手工任务',
  'RECEIVE TASK': '接收任务',
  'SEND TASK': '发送任务',
  'CALL ACTIVITY': '调用活动',
  'SUB PROCESS': '子流程',
  'EXPANDED SUB PROCESS': '展开子流程',
  'COLLAPSED SUB PROCESS': '折叠子流程',
  'EVENT SUB PROCESS': '事件子流程',
  'EXCLUSIVE GATEWAY': '排他网关',
  'PARALLEL GATEWAY': '并行网关',
  'INCLUSIVE GATEWAY': '包容网关',
  'EVENT BASED GATEWAY': '事件网关',
  'COMPLEX GATEWAY': '复杂网关',
  'SEQUENCE FLOW': '顺序流',
  'MESSAGE FLOW': '消息流',
  'ASSOCIATION': '关联',
  'DATA FLOW': '数据流',
  'POOL': '池',
  'LANE': '泳道',
  'PARTICIPANT': '参与者',
  'DATA OBJECT': '数据对象',
  'DATA OBJECT REFERENCE': '数据对象引用',
  'DATA STORE': '数据存储',
  'DATA STORE REFERENCE': '数据存储引用',
  'GROUP': '组',
  'TEXT ANNOTATION': '文本注释',
  'COLLABORATION': '协作',
  'PROCESS': '流程',
  'TASK': '任务',

  // 其他可能的标题形式
  'Start Event': '开始事件',
  'End Event': '结束事件',
  'Intermediate Catch Event': '中间捕获事件',
  'Intermediate Throw Event': '中间抛出事件',
  'Boundary Event': '边界事件',
  'User Task': '用户任务',
  'Service Task': '服务任务',
  'Script Task': '脚本任务',
  'Business Rule Task': '业务规则任务',
  'Manual Task': '手工任务',
  'Receive Task': '接收任务',
  'Send Task': '发送任务',
  'Call Activity': '调用活动',
  'Sub Process': '子流程',
  'Expanded Sub Process': '展开子流程',
  'Collapsed Sub Process': '折叠子流程',
  'Event Sub Process': '事件子流程',
  'Exclusive Gateway': '排他网关',
  'Parallel Gateway': '并行网关',
  'Inclusive Gateway': '包容网关',
  'Event Based Gateway': '事件网关',
  'Complex Gateway': '复杂网关',
  'Sequence Flow': '顺序流',
  'Message Flow': '消息流',
  'Data Object Reference': '数据对象引用',
  'Data Store Reference': '数据存储引用',
  'Text Annotation': '文本注释'
};

/**
 * 自定义翻译函数
 * @param template 翻译模板
 * @param replacements 替换参数
 * @returns 翻译后的文本
 */
export function customTranslate(template: string, replacements?: Record<string, any>): string {
  replacements = replacements || {};

  // 翻译
  template = zhCN[template] || template;

  // 替换参数
  return template.replace(/{([^}]+)}/g, function (_, key) {
    return replacements[key] || '{' + key + '}';
  });
}

// 导出翻译模块
export default {
  translate: ['value', customTranslate]
};
